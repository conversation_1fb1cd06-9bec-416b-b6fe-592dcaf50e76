<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Massage Price Calculator</title>

    <!-- External CSS and Font Resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Lato:300,400|Poppins:300,400,800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap" rel="stylesheet">



    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles/components/booking-form.css">
</head>
<body>
    <!-- Main Calculator Card -->
    <main class="calculator-card">
        <!-- Card Header -->
        <header class="card-header">
            <h1 class="text-2xl font-bold" style="font-family: 'Poppins', sans-serif; letter-spacing: -0.045em; text-shadow: 1px 1.01px 1.01px rgba(0,0,0,0.3); color: #fff;">Massage Price Calculator</h1>
            <p class="text-sm mt-1 opacity-90" style="font-family: 'Lato', sans-serif; letter-spacing: 1.3px; color: rgba(255,255,255,0.9);">Build your custom massage experience</p>
        </header>

        <!-- Card Body Content -->
        <div class="card-body">

            <!-- Rive Animation Container with Current Relaxation Level -->
            <section id="animation-container" aria-label="Relaxation level visualization">
                <div id="rive-canvas-container" class="rive-canvas-container centered" aria-hidden="true">
                    <canvas class="rounded-full" id="canvas" width="160" height="160" aria-label="Relaxation animation"></canvas>
                </div>
                <div id="relaxation-text" class="current-relaxation-level hidden-text" role="status" aria-live="polite">
                    <div class="relaxation-title">Relaxation Level</div>
                    <div id="relaxation-level-text" class="relaxation-value">Calm</div>
                </div>
            </section>
            <!-- Load Rive from local installation -->
            <script src="scripts/vendor/rive/rive.js"></script>
            <script src="scripts/modules/rive-lotus.js"></script>
            <!-- End of Rive Animation Container -->

            <!-- Person Selection Section -->
            <section class="mb-6" id="person-section" aria-labelledby="person-section-title">
                <div class="flex justify-between items-center mb-2" id="person-header">
                    <h2 id="person-section-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Choose Number of People</h2>
                    <button id="person-next-button" class="text-white font-medium py-1 px-4 rounded-md duration-300 shadow-md hidden" style="height: 36px;  border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Lato', sans-serif;" aria-label="Continue to massage style selection">
                        Next <i class="fas fa-arrow-right ml-1" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Person summary that will replace the header -->
                <div id="person-summary" class="hidden p-3 rounded-lg mb-2 font-medium shadow-sm flex justify-between items-center" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif;" role="region" aria-live="polite">
                    <span id="person-summary-text"></span>
                    <button id="edit-person-button" class="text-sm font-medium px-2 py-1 rounded-md transition-colors edit-button" style="height: 32px; border: solid 1px; color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit number of people selection">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Person options grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3" id="person-options" role="radiogroup" aria-labelledby="person-section-title">
                    <!-- Person options will be populated by JavaScript -->
                </div>
            </section>

            <!-- Service Selection Section -->
            <section class="mb-6 hidden" id="service-section" aria-labelledby="service-section-title">
                <div class="flex justify-between items-center mb-2" id="service-header">
                    <h2 id="service-section-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Choose Your Massage Style</h2>
                    <button id="service-next-button" class="text-white font-medium py-1 px-4 rounded-md duration-300 shadow-md hidden" style="height: 36px;  border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Lato', sans-serif;" aria-label="Continue to duration selection">
                        Next <i class="fas fa-arrow-right ml-1" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Service summary that will replace the header -->
                <div id="service-summary" class="hidden p-3 rounded-lg mb-2 font-medium shadow-sm flex justify-between items-center" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif;" role="region" aria-live="polite">
                    <span id="service-summary-text"></span>
                    <button id="edit-service-button" class="text-sm font-medium px-2 py-1 rounded-md transition-colors edit-button" style="height: 32px; border: solid 1px; color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit massage style selection">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Service options grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3" id="service-options" role="radiogroup" aria-labelledby="service-section-title">
                    <!-- Service options will be populated by JavaScript -->
                </div>
            </section>

            <!-- Corporate Sessions Selection Section (for Corporate only) -->
            <section class="mb-6 hidden" id="unified-selection-section" aria-labelledby="unified-selection-title">
                <div class="flex justify-between items-center mb-3" id="unified-selection-header">
                    <h2 id="unified-selection-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Book Corporate Chair Massage Sessions</h2>
                    <button id="unified-selection-next-button" class="text-white font-medium py-1 px-4 rounded-md duration-300 shadow-md hidden" style="height: 36px; border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Lato', sans-serif;" aria-label="Continue to booking summary">
                        Next <i class="fas fa-arrow-right ml-1" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Corporate sessions summary -->
                <div id="unified-selection-summary" class="hidden p-3 rounded-lg mb-2 font-medium shadow-sm" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif; display: flex; justify-content: space-between; align-items: center;" role="region" aria-live="polite">
                    <span id="unified-selection-summary-text"></span>
                    <button id="edit-unified-selection-button" class="text-sm font-medium px-2 py-1 rounded-md transition-colors edit-button" style="height: 32px; border: solid 1px; color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit session configuration">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Corporate sessions interface -->
                <div class="corporate-sessions-interface" id="unified-selection-interface">
                    <div class="corporate-sessions-card">
                        <div class="corporate-sessions-header">
                            <div class="sessions-info">
                                <h3 class="sessions-title">15-minute sessions • $25 each</h3>
                                <p class="sessions-subtitle">Minimum 4 sessions required</p>
                            </div>
                        </div>

                        <div class="corporate-sessions-controls">
                            <div class="sessions-counter">
                                <label class="sessions-label">Number of Sessions:</label>
                                <div class="sessions-counter-controls">
                                    <button id="corporate-decrease-sessions" class="counter-button" aria-label="Decrease sessions by 4 (1 hour)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <div class="sessions-display">
                                        <span id="corporate-sessions-display">4</span> sessions
                                    </div>
                                    <button id="corporate-increase-sessions" class="counter-button" aria-label="Increase sessions by 4 (1 hour)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="corporate-sessions-summary">
                            <div class="summary-row">
                                <span class="summary-label">Total Time:</span>
                                <span id="corporate-total-time" class="summary-value">1 hour</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Total Price:</span>
                                <span id="corporate-total-price" class="summary-value">$100</span>
                            </div>
                        </div>
                    </div>

                    <div class="corporate-sessions-info">
                        <p id="corporate-sessions-info" class="text-sm" style="color: #666; font-family: 'Lato', sans-serif; text-align: center; margin-top: 1rem; line-height: 1.4;">
                            <i class="fas fa-info-circle" style="margin-right: 0.5rem; color: hsl(184, 70%, 35%);"></i>
                            15-minute sessions • $25 each • Minimum 4 sessions required
                        </p>
                    </div>
                </div>
            </section>

            <!-- Duration Selection Section (for Individual/Couples) -->
            <section class="mb-6 hidden" id="duration-section" aria-labelledby="duration-section-title">
                <div class="flex justify-between items-center mb-3">
                    <h2 id="duration-section-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Choose Your Duration</h2>
                    <button id="edit-duration-button" class="text-sm font-medium hidden px-2 py-1 rounded-md transition-colors edit-button" style="color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit duration selection">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Duration options container -->
                <div class="flex flex-wrap gap-2" id="duration-options" role="radiogroup" aria-labelledby="duration-section-title">
                    <!-- Duration options will be populated by JavaScript -->
                </div>

                <!-- Duration summary display -->
                <div id="duration-summary" class="hidden p-3 rounded-lg mt-3 font-medium shadow-sm" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif;" role="region" aria-live="polite"></div>
            </section>

            <!-- Book Now Button Section -->
            <section id="book-now-container" class="hidden text-center" aria-labelledby="booking-ready-title">
                <div id="booking-ready-title" class="mb-3 text-xl" style="color: #444; font-family: 'Poppins', sans-serif; font-weight: 600; line-height: 1.4; text-shadow: 0 1px 1px rgba(255,255,255,0.8);" role="status" aria-live="polite">
                    Ready for your relaxation journey!
                </div>
                <button id="book-button" class="book-now-button text-white font-bold py-3 px-8 rounded-xl transition duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed" style="border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Poppins', sans-serif; text-transform: uppercase; letter-spacing: 2px; font-size: 1.25rem;" disabled aria-label="Complete booking">
                    <i class="fas fa-calendar-check mr-2" aria-hidden="true"></i> Book Now
                </button>
            </section>
        </div>
    </main>

    <!-- Main Application JavaScript -->
    <script src="scripts/modules/booking-form.js"></script>
</body>
</html>
